base:
  buffer:
    initial_size: 1024      # 缓冲区初始大小,单位字节
    max_size: 65536         # 缓冲区最大大小,单位字节
    growth_factor: 2        # 缓冲区扩展倍数

log:
  basename: logs/server     # 日志文件基础名
  roll_size: 1048576        # 日志滚动大小,单位字节
  flush_interval: 1         # 日志刷新间隔,单位秒
  roll_mode: SIZE_HOURLY    # 日志滚动模式(SIZE_HOURLY/其他)
  enable_file: true         # 是否启用文件日志
  enable_async: true        # 是否启用异步日志,false=同步,true=异步
  file_level: DEBUG         # 文件日志级别
  console_level: WARN       # 控制台日志级别

network:
  ip: 127.0.0.1             # 服务器监听IP地址
  port: 8443                # 服务器监听端口
  epoll_mode: "LT"          # epoll触发模式,可选 "ET" 或 "LT"
  idle_timeout: 30          # 空闲连接超时时间,单位秒
  ssl:
    enable: true # 是否启用HTTPS
    cert_path: "certs/server.crt" # 服务器证书路径
    key_path: "certs/server.key"  # 服务器私钥路径
  thread_pool:
    thread_num: 3           # 线程池线程数量
    queue_size: 1000        # 线程池任务队列大小
    keep_alive_time: 60     # 线程池线程保活时间,单位秒
    max_idle_threads: 5     # 线程池最大空闲线程数
    min_idle_threads: 1     # 线程池最小空闲线程数

database:
  host: "127.0.0.1"           # 数据库主机地址，通常为本机或远程服务器IP
  user: "ayp"                 # 数据库用户名
  password: "801026qwe"       # 数据库密码
  dbname: "webserver"         # 需要连接的数据库名
  port: 3306                  # 数据库端口号，MySQL默认3306
  initSize: 5                 # 连接池初始连接数，建议小于maxSize
  maxSize: 20                 # 连接池最大连接数，建议根据业务压力调整
  maxIdleTime: 60             # 连接最大空闲时间(秒)，超时将被回收
  connectionTimeout: 1000     # 获取连接的超时时间(毫秒)，超时返回nullptr

jwt:
  secret: "A7f$kLz9@3#xQw7$eRtYpLmN2sVbC4uH!pZx8Qw2#rTg6$MnB"  # JWT签名密钥
  expire_seconds: 86400        # Token有效期（秒），如86400为24小时
  issuer: "webserver"          # JWT签发者标识，可用于区分不同服务或环境