@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

:root {
  --bg-primary: #1a1d24;
  --bg-secondary: #242832;
  --bg-tertiary: #2d323d;
  --text-primary: #e2e2e2;
  --text-secondary: #a0a0a0;
  --accent-primary: #007bff;
  --accent-hover: #3395ff;
  --border-color: #3a404d;
  --shadow-color: rgba(0, 0, 0, 0.2);
  --success: #28a745;
  --danger: #dc3545;
  --font-family: 'Noto Sans SC', sans-serif;
  --border-radius: 8px;
  --sidebar-width: 240px;
  --transition-speed: 0.3s;
}

/* --- Animations --- */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }

  80% {
    opacity: 1;
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes backgroundGradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}


/* --- Base & Layout --- */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: var(--font-family);
  background: linear-gradient(-45deg, #1a1d24, #2d323d, #1a1d24);
  background-size: 400% 400%;
  animation: backgroundGradient 15s ease infinite;
  color: var(--text-primary);
  overflow-x: hidden;
}

.app-layout {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: var(--sidebar-width);
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100%;
  transition: width var(--transition-speed) ease;
  box-shadow: 2px 0 10px var(--shadow-color);
}

.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  padding: 2.5rem;
  transition: margin-left var(--transition-speed) ease;
}

.main-content>* {
  animation: fadeInUp 0.5s ease-out forwards;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
}

/* --- Sidebar --- */
.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.logo {
  color: var(--text-primary);
  text-decoration: none;
  font-size: 1.5rem;
  font-weight: 700;
}

.sidebar-nav {
  flex: 1;
  padding-top: 1rem;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s, color 0.2s, border-left-color 0.2s;
  border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.sidebar-nav a.active {
  background-color: var(--bg-primary);
  color: var(--accent-primary);
  border-left-color: var(--accent-primary);
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.auth-area-content {
  display: flex;
  align-items: center;
}

.auth-area .username {
  font-weight: 500;
  margin-right: auto;
}

.auth-area a,
.auth-area button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1rem;
  font-family: var(--font-family);
  transition: color 0.2s;
}

.auth-area a:hover,
.auth-area button:hover {
  color: var(--accent-primary);
}

/* --- Icons --- */
[class^="icon-"] {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 1rem;
  background-color: currentColor;
  mask-repeat: no-repeat;
  mask-size: contain;
  transition: transform 0.2s;
}

.sidebar-nav a:hover .icon-home,
.sidebar-nav a.active .icon-home {
  transform: scale(1.1);
}

.icon-home {
  mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>');
}

.icon-socket {
  mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12.5 13.16l-3.32 3.32A5.5 5.5 0 0 1 3.66 8.5l3.32-3.32 1.41 1.41-3.32 3.32a3.5 3.5 0 0 0 4.95 4.95l3.32-3.32-1.41-1.42zm-1.41-1.42l3.32-3.32a5.5 5.5 0 0 1 7.78 7.78l-3.32 3.32-1.41-1.41 3.32-3.32a3.5 3.5 0 0 0-4.95-4.95l-3.32 3.32 1.41 1.42z"/></svg>');
}

.icon-about {
  mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>');
}

/* --- General Components --- */
.page-title {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
  color: var(--text-primary);
}

.card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 2.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 12px var(--shadow-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px var(--shadow-color);
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--accent-primary);
  border-radius: var(--border-radius);
  background-color: var(--accent-primary);
  color: white;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.btn:hover {
  background-color: var(--accent-hover);
  border-color: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn:active {
  transform: translateY(0);
  box-shadow: none;
}

.btn:disabled {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* --- Form --- */
.form-container {
  max-width: 450px;
  margin: 2rem auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.form-text {
  text-align: center;
  margin-top: 1.5rem;
}

.form-text a {
  color: var(--accent-primary);
  text-decoration: none;
  font-weight: 500;
}

.form-message {
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius);
  text-align: center;
  animation: fadeInUp 0.3s;
}

.form-message.success {
  background-color: rgba(40, 167, 69, 0.2);
  color: var(--success);
}

.form-message.error {
  background-color: rgba(220, 53, 69, 0.2);
  color: var(--danger);
}

/* --- WebSocket Chat --- */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 65vh;
}

.chat-messages {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  overflow-y: auto;
  margin-bottom: 1rem;
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
}

.chat-messages div {
  margin-bottom: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  max-width: 70%;
  word-wrap: break-word;
  line-height: 1.4;
  animation: popIn 0.3s ease-out;
}

.chat-messages .sent {
  background-color: var(--accent-primary);
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 0;
}

.chat-messages .received {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  margin-right: auto;
  border-bottom-left-radius: 0;
}

.chat-input-form {
  display: flex;
}

.chat-input-form input {
  flex: 1;
  margin-right: 1rem;
}

.chat-status {
  text-align: center;
  padding: 0.5rem;
  font-style: italic;
  color: var(--text-secondary);
}

.chat-status .status-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
  transition: background-color 0.3s;
}

.chat-status .status-dot.connected {
  background-color: var(--success);
}

.chat-status .status-dot.disconnected {
  background-color: var(--danger);
}

.chat-status .status-dot.connecting {
  background-color: #ffc107;
  animation: pulse 1.5s infinite;
}

/* --- Enterprise Error Page Styles --- */
@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }

  100% {
    transform: translateY(0px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.error-page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  width: 100%;
  animation: fadeIn 0.5s ease-out;
}

.error-page-container .graphic {
  width: 100%;
  max-width: 400px;
  margin-bottom: 2rem;
  animation: float 6s ease-in-out infinite;
}

.error-page-container h1 {
  font-size: 4rem;
  margin: 0;
  color: var(--accent-primary);
  font-weight: 700;
}

.error-page-container h2 {
  font-size: 1.75rem;
  margin: 0.5rem 0 1rem 0;
  color: var(--text-primary);
}

.error-page-container p {
  color: var(--text-secondary);
  max-width: 450px;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.error-page-container .btn {
  margin-top: 1rem;
}